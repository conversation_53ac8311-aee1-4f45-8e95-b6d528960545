import { pgTable, text, serial, integer, boolean, timestamp, json } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Define full report data structure
export interface FullReportData {
  executiveSummary: string;
  detailedScores: Array<{
    name: string;
    score: number;
    explanation: string;
    examples: string;
    codeSnippet?: string;
    suggestions: string;
  }>;
  techStack: {
    technologies: string[];
    pros: string[];
    cons: string[];
    recommendations: string[];
    alternatives: string[];
  };
  feedbackStrategy: {
    currentScore: number;
    gaps: string;
    guide: string;
    codeSnippet?: string;
    bestPractices: string[];
    tools: string[];
  };
  deploymentChecklist: {
    currentScore: number;
    gaps: string;
    hosting: string;
    performance: string;
    security: string;
    monitoring: string;
    timeline: string;
  };
  testingRecommendations: {
    currentScore: number;
    gaps: string[];
    frameworks: string[];
    sampleTests: string;
    userTestingPlan: string;
  };
  documentationPlan: {
    currentScore: number;
    gaps: string[];
    readmeTemplate: string;
    tools: string[];
    examples: string;
  };
  benchmarking: {
    score: number;
    averageScore: number;
    strengths: string[];
    weaknesses: string[];
  };
  resourceLibrary: {
    tutorials: string[];
    tools: string[];
    communities: string[];
    templates: string[];
  };
  roadmap: {
    currentLevel: string;
    targetLevel: string;
    steps: Array<{
      title: string;
      priority: 'High' | 'Medium' | 'Low';
      estimatedTime: string;
    }>;
    timeline: string;
  };
  visualizations: {
    radarChartData: any;
    codeQualityMetrics: Record<string, string | number>;
    performanceMetrics?: Record<string, string | number>; // Optional - can't measure from static code
    comparisonData: any;
  };
};

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

// Define analysis table for storing MVP scoring data
export const analyses = pgTable("analyses", {
  id: serial("id").primaryKey(),
  type: text("type").notNull(), // 'file' or 'github'
  repoUrl: text("repo_url"),
  description: text("description"),
  projectType: text("project_type").notNull(),
  status: text("status").notNull(), // 'processing', 'completed', 'failed'
  progress: json("progress").$type<any>(),
  projectInfo: json("project_info").$type<any>(),
  scoreData: json("score_data").$type<any>(),
  
  // Fields for premium full report
  fullReport: json("full_report").$type<any>(),
  reportUrl: text("report_url"), // S3 URL for PDF
  paymentStatus: text("payment_status").default('pending'), // 'pending', 'completed'
  userId: integer("user_id"), // Link to user who purchased the report
  
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const insertAnalysisSchema = createInsertSchema(analyses).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  progress: true,
  projectInfo: true,
  scoreData: true,
  fullReport: true,
  reportUrl: true,
  userId: true
});

export type InsertAnalysis = z.infer<typeof insertAnalysisSchema>;
export type Analysis = typeof analyses.$inferSelect;
