#!/bin/bash

# Chrome Dependencies Installation Script for PDF Generation
# This script installs the required system libraries for Puppeteer/Chrome

echo "🔧 Installing Chrome dependencies for PDF generation..."

# Check if running as root/sudo
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script needs to be run with sudo privileges"
    echo "Please run: sudo ./install-chrome-deps.sh"
    exit 1
fi

# Update package list
echo "📦 Updating package list..."
apt-get update

# Install required Chrome dependencies
echo "🚀 Installing Chrome dependencies..."
apt-get install -y \
    libnss3 \
    libnspr4 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    libatspi2.0-0 \
    libgtk-3-0 \
    libgdk-pixbuf2.0-0

# Optional: Install Chrome browser (if not using Puppeteer's bundled Chrome)
# echo "🌐 Installing Google Chrome (optional)..."
# wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add -
# echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list
# apt-get update
# apt-get install -y google-chrome-stable

echo "✅ Chrome dependencies installed successfully!"
echo ""
echo "🎯 You can now run the application with PDF generation support:"
echo "   npm run dev"
echo ""
echo "🔍 If you still get errors, try setting the Chrome executable path:"
echo "   export PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome"
echo "   npm run dev"
